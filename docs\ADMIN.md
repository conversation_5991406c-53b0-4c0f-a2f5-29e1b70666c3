# GuardGeo API Platform - Admin Interface Documentation

## Overview

The GuardGeo API Platform includes a comprehensive web-based administration interface for monitoring, managing, and analyzing the system. The admin interface provides role-based access control with different permission levels for various team members.

## Access

**URL:** `https://your-domain.com/admin`

**Default Credentials:**
- **Email:** <EMAIL>
- **Password:** admin123456789

⚠️ **Important:** Change the default password immediately after first login.

## User Roles

### Super Admin
- **Full system access** and user management
- Create and manage all other admin accounts
- Access to all features and sensitive data
- System configuration and maintenance tools

### Developer
- **Technical administration** and debugging tools
- Access to logs, error reports, and system diagnostics
- API testing and development tools
- Database and cache management

### Marketing
- **Analytics and marketing data** access
- User behavior and geographic analytics
- Campaign performance metrics
- Customer insights and reports

### Sales
- **Sales analytics and customer data** access
- Revenue and subscription metrics
- Customer lifecycle analytics
- Conversion and retention reports

## Features

### Dashboard

The main dashboard provides an overview of system health and key metrics:

#### System Status
- **API Health**: Current API status and response times
- **Database Status**: Connection health and query performance
- **Cache Status**: Redis connectivity and hit rates
- **Service Status**: External service availability (ipRegistry, Freemius)

#### Usage Statistics
- **Total API Requests**: Daily, weekly, and monthly request counts
- **Success Rate**: Percentage of successful API calls
- **Error Rate**: Failed requests and error categorization
- **Response Times**: Average and percentile response times

#### Geographic Analytics
- **Request Distribution**: Requests by country and region
- **IP Analysis Results**: Security threat detection statistics
- **Top Countries**: Most active geographic regions
- **Threat Detection**: VPN, Tor, and proxy detection rates

#### Recent Activity
- **Latest API Requests**: Real-time request monitoring
- **Recent Errors**: Latest error occurrences
- **Admin Actions**: Recent administrative activities
- **System Events**: Important system notifications

### Log Viewer

Comprehensive log analysis and search functionality:

#### Search and Filtering
- **Date Range**: Filter logs by specific time periods
- **Log Level**: Filter by ERROR, WARN, INFO, DEBUG levels
- **IP Address**: Search for specific IP addresses
- **Endpoint**: Filter by API endpoint
- **Status Code**: Filter by HTTP response codes
- **User Agent**: Search by client user agent

#### Log Categories
- **API Logs**: All API request and response data
- **Error Logs**: System errors and exceptions
- **Admin Logs**: Administrative actions and changes
- **Webhook Logs**: Freemius webhook processing
- **Security Logs**: Authentication and security events

#### Export Options
- **CSV Export**: Download filtered logs as CSV
- **JSON Export**: Export logs in JSON format
- **Real-time Monitoring**: Live log streaming
- **Automated Reports**: Scheduled log summaries

### IP Lookup Tool

Manual IP address analysis and testing:

#### Lookup Features
- **Single IP Analysis**: Analyze individual IP addresses
- **Bulk IP Analysis**: Process multiple IPs simultaneously
- **Historical Data**: View cached IP analysis results
- **Force Refresh**: Bypass cache and fetch fresh data

#### Analysis Results
- **Geographic Location**: Country, region, city, coordinates
- **Security Assessment**: Threat detection and risk scoring
- **Network Information**: ISP, ASN, connection type
- **Anonymization Detection**: VPN, proxy, Tor identification
- **Carrier Information**: Mobile carrier details (if applicable)

#### Testing Tools
- **API Endpoint Testing**: Test API calls with custom parameters
- **Response Validation**: Verify API response format and data
- **Performance Testing**: Measure response times and reliability
- **Error Simulation**: Test error handling and edge cases

### Data Synchronization

Freemius data management and synchronization:

#### Sync Operations
- **Manual Sync**: Force synchronization with Freemius API
- **Incremental Sync**: Update only changed data
- **Full Refresh**: Complete data rebuild from Freemius
- **Selective Sync**: Sync specific products or installations

#### Data Management
- **Product Management**: View and manage Freemius products
- **Installation Tracking**: Monitor active installations
- **License Validation**: Verify license status and expiration
- **Subscription Monitoring**: Track subscription changes

#### Webhook Management
- **Webhook Status**: Monitor webhook delivery and processing
- **Event Queue**: View pending webhook events
- **Retry Management**: Handle failed webhook deliveries
- **Signature Validation**: Verify webhook authenticity

### User Management (Super Admin Only)

Administrative user account management:

#### Account Creation
- **Role Assignment**: Assign appropriate user roles
- **Domain Validation**: Ensure email domains are authorized
- **Password Requirements**: Enforce strong password policies
- **Account Activation**: Enable/disable user accounts

#### Permission Management
- **Role-based Access**: Control feature access by role
- **Custom Permissions**: Fine-grained permission control
- **Session Management**: Monitor active user sessions
- **Security Policies**: Enforce security requirements

#### Audit Trail
- **Login History**: Track user authentication events
- **Action Logging**: Record all administrative actions
- **Permission Changes**: Log role and permission modifications
- **Security Events**: Monitor suspicious activities

## Security Features

### Authentication
- **Email/Password**: Secure login with strong password requirements
- **Domain Restriction**: Limit access to authorized email domains
- **Session Management**: Automatic session timeout and renewal
- **Failed Login Protection**: Account lockout after failed attempts

### Authorization
- **Role-based Access Control**: Granular permission system
- **Feature Restrictions**: Role-specific feature access
- **Data Isolation**: Users see only authorized data
- **Action Validation**: Verify permissions before actions

### Security Monitoring
- **Login Tracking**: Monitor all authentication attempts
- **IP Whitelisting**: Restrict access by IP address (optional)
- **Suspicious Activity Detection**: Alert on unusual patterns
- **Security Audit Logs**: Comprehensive security event logging

## Configuration

### System Settings
- **Debug Mode**: Enable/disable debug logging
- **Log Levels**: Configure logging verbosity
- **Cache Settings**: Adjust cache TTL and policies
- **Rate Limiting**: Configure API rate limits

### API Configuration
- **ipRegistry Settings**: Configure API key and endpoints
- **Freemius Settings**: Webhook secrets and API configuration
- **Sentry Integration**: Error tracking and monitoring setup
- **Performance Tuning**: Optimize response times and throughput

### Notification Settings
- **Email Alerts**: Configure error and security notifications
- **Webhook Notifications**: Set up external integrations
- **Threshold Alerts**: Alert on performance or error thresholds
- **Maintenance Notifications**: System maintenance announcements
