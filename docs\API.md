# GuardGeo API Platform - API Documentation

## Overview

The GuardGeo API Platform provides RESTful endpoints for IP intelligence and fraud prevention. All API endpoints return JSON responses and require proper authentication through Freemius integration.

## Base URL

```
Production: https://api.guardgeowp.com
Development: http://localhost:8080
```

## Authentication

All API requests must include valid Freemius installation data for authentication. The system validates:

1. **Plugin ID**: Must exist as an active product in Freemius
2. **Installation ID**: Must belong to the specified plugin
3. **Installation Status**: Must be active and not uninstalled
4. **License Status**: Must have valid subscription/license (for premium features)

## Rate Limiting

- **API Requests**: 60 requests per minute per IP address
- **Webhook Requests**: 100 requests per minute per IP address

Rate limit headers are included in all responses:
```
X-RateLimit-Limit: 60
X-RateLimit-Remaining: 59
X-RateLimit-Reset: **********
```

## Endpoints

### Health Check

Check API service status and connectivity.

**Endpoint:** `GET /api/v1/health`

**Response:**
```json
{
  "success": true,
  "status": "healthy",
  "timestamp": "2025-01-20T18:30:00Z",
  "version": "1.0.0",
  "services": {
    "database": "connected",
    "redis": "connected",
    "ipregistry": "available"
  }
}
```

### IP Analysis

Analyze visitor IP addresses for security threats and geolocation data.

**Endpoint:** `POST /api/v1/analyze`

**Request Headers:**
```
Content-Type: application/json
User-Agent: GuardGeo-Plugin/1.0.0
```

**Request Body:**
```json
{
  "ip": "*************",
  "visitor_hash": "uuid-v4-string",
  "plugin_id": 12345,
  "install_id": 67890,
  "url": "https://example.com"
}
```

**Parameters:**
- `ip` (string, required): IPv4 or IPv6 address to analyze
- `visitor_hash` (string, required): Unique visitor identifier (UUID v4)
- `plugin_id` (integer, required): Freemius plugin/product ID
- `install_id` (integer, required): Freemius installation ID
- `url` (string, required): Website URL where plugin is installed

**Success Response (200):**
```json
{
  "success": true,
  "data": {
    "ip": "**********",
    "type": "IPv4",
    "hostname": "host.example.com",
    "carrier": {
      "name": "Verizon Wireless",
      "mcc": "310",
      "mnc": "004"
    },
    "company": {
      "domain": "verizon.com",
      "name": "Verizon Communications Inc.",
      "type": "isp"
    },
    "connection": {
      "asn": 22394,
      "domain": "verizon.com",
      "organization": "Verizon Wireless",
      "route": "**********/16",
      "type": "cellular"
    },
    "location": {
      "continent": {
        "code": "NA",
        "name": "North America"
      },
      "country": {
        "code": "US",
        "name": "United States",
        "flag": {
          "emoji": "🇺🇸"
        }
      },
      "region": {
        "code": "CA",
        "name": "California"
      },
      "city": "Mountain View",
      "postal": "94043",
      "latitude": 37.419200897216797,
      "longitude": -122.05740356445312,
      "in_eu": false
    },
    "security": {
      "is_abuser": false,
      "is_attacker": false,
      "is_bogon": false,
      "is_cloud_provider": false,
      "is_proxy": false,
      "is_relay": false,
      "is_tor": false,
      "is_tor_exit": false,
      "is_vpn": false,
      "is_anonymous": false,
      "is_threat": false
    },
    "time_zone": {
      "id": "America/Los_Angeles",
      "abbreviation": "PST",
      "current_time": "2025-01-20T02:30:00-08:00",
      "name": "Pacific Standard Time",
      "offset": -28800,
      "in_daylight_saving": false
    },
    "_meta": {
      "cached_at": "2025-01-20T10:30:00Z",
      "fresh_until": "2025-01-23T10:30:00Z"
    }
  },
  "request_id": "req_1234567890abcdef"
}
```

## Webhooks

### Freemius Webhooks

Receive real-time updates from Freemius for installation and subscription changes.

**Endpoint:** `POST /webhooks/freemius`

**Authentication:** HMAC-SHA256 signature verification

**Headers:**
```
X-FS-Signature: sha256=calculated_signature
Content-Type: application/json
```

**Supported Events:**
- `install.activated`
- `install.deactivated`
- `install.uninstalled`
- `subscription.created`
- `subscription.updated`
- `subscription.cancelled`
- `license.activated`
- `license.deactivated`

## Error Handling

### Error Response Format

All errors follow a consistent JSON structure:

```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "Human readable error message",
    "details": "Additional context when available"
  },
  "request_id": "req_1234567890abcdef"
}
```

### Error Codes

#### Validation Errors (400)
- `MISSING_PARAMETER`: Required parameter is missing
- `INVALID_IP`: IP address format is invalid
- `INVALID_PLUGIN_ID`: Plugin ID is not a valid integer
- `INVALID_INSTALL_ID`: Installation ID is not a valid integer
- `INVALID_URL`: URL format is invalid

#### Authentication Errors (401/403)
- `INVALID_INSTALLATION`: Installation not found or inactive
- `UNINSTALLED_PLUGIN`: Plugin has been uninstalled
- `INACTIVE_LICENSE`: License is expired or inactive
- `UNAUTHORIZED_DOMAIN`: Request from unauthorized domain

#### Service Errors (500)
- `DATABASE_ERROR`: Database connection or query failed
- `REDIS_ERROR`: Cache service unavailable
- `IPREGISTRY_ERROR`: IP intelligence service unavailable
- `INTERNAL_ERROR`: Unexpected server error

#### Rate Limiting (429)
- `RATE_LIMIT_EXCEEDED`: Too many requests from IP address
- `API_QUOTA_EXCEEDED`: API usage quota exceeded

## Security Features

### IP Intelligence Data

The API returns comprehensive security analysis including:

- **VPN Detection**: Identifies VPN and proxy services
- **Tor Detection**: Detects Tor exit nodes and relays
- **Threat Analysis**: Flags known malicious IP addresses
- **Anonymization**: Identifies anonymous proxy services
- **Cloud Providers**: Detects hosting and cloud services
- **Abuse History**: Checks against abuse databases

### Data Privacy

- **No Personal Data**: Only IP addresses and technical data are processed
- **Data Retention**: IP data cached for 3 days maximum
- **Secure Transmission**: All data encrypted in transit
- **Access Logging**: All requests logged for security monitoring

## Integration Examples

### WordPress Plugin Integration

```php
<?php
// GuardGeo WordPress Plugin - API Integration Example

function guardgeo_analyze_visitor($visitor_ip) {
    $api_url = 'https://api.guardgeowp.com/api/v1/analyze';
    
    $data = [
        'ip' => $visitor_ip,
        'visitor_hash' => wp_generate_uuid4(),
        'plugin_id' => GUARDGEO_PLUGIN_ID,
        'install_id' => get_option('guardgeo_install_id'),
        'url' => home_url()
    ];
    
    $response = wp_remote_post($api_url, [
        'headers' => [
            'Content-Type' => 'application/json',
            'User-Agent' => 'GuardGeo-Plugin/' . GUARDGEO_VERSION
        ],
        'body' => json_encode($data),
        'timeout' => 10
    ]);
    
    if (is_wp_error($response)) {
        return false;
    }
    
    $body = wp_remote_retrieve_body($response);
    $result = json_decode($body, true);
    
    if ($result['success']) {
        return $result['data'];
    }
    
    return false;
}
```

### cURL Example

```bash
curl -X POST https://api.guardgeowp.com/api/v1/analyze \
  -H "Content-Type: application/json" \
  -H "User-Agent: GuardGeo-Client/1.0" \
  -d '{
    "ip": "*******",
    "visitor_hash": "550e8400-e29b-41d4-a716-************",
    "plugin_id": 12345,
    "install_id": 67890,
    "url": "https://example.com"
  }'
```

## Support

- **Technical Documentation**: https://docs.guardgeowp.com
- **API Status**: https://status.guardgeowp.com
- **Support Email**: <EMAIL>
- **Developer Forum**: https://community.guardgeowp.com
