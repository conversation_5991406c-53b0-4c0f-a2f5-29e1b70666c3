-- GuardGeo API Platform - Consolidated Database Schema
-- PostgreSQL schema for the GuardGeo API Platform
-- This file creates all necessary tables, indexes, and constraints with conflict protection

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create enum types with conflict protection
DO $$ BEGIN
    CREATE TYPE user_role AS ENUM ('super_admin', 'dev', 'marketing', 'sales');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE refund_policy_type AS ENUM ('flexible', 'moderate', 'strict');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE renewals_discount_type AS ENUM ('percentage', 'dollar');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE product_type AS ENUM ('plugin', 'theme', 'widget', 'template');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Products table (Freemius products/plugins)
CREATE TABLE IF NOT EXISTS products (
    id BIGSERIAL PRIMARY KEY,
    freemius_id BIGINT UNIQUE NOT NULL,
    secret_key VARCHAR(255) NOT NULL,
    public_key VARCHAR(255) NOT NULL,
    parent_plugin_id BIGINT NULL,
    developer_id BIGINT NOT NULL,
    store_id BIGINT NOT NULL,
    slug VARCHAR(255) NOT NULL,
    title VARCHAR(255) NOT NULL,
    environment INTEGER NOT NULL DEFAULT 0, -- 0=production, 1=sandbox
    icon VARCHAR(500) NULL,
    default_plan_id BIGINT NOT NULL,
    plans TEXT NULL, -- comma separated plan IDs
    features TEXT NULL, -- comma separated feature IDs
    money_back_period INTEGER NOT NULL DEFAULT 0,
    refund_policy refund_policy_type NOT NULL DEFAULT 'moderate',
    annual_renewals_discount INTEGER NULL,
    renewals_discount_type renewals_discount_type NULL,
    is_released BOOLEAN NOT NULL DEFAULT FALSE,
    is_sdk_required BOOLEAN NOT NULL DEFAULT TRUE,
    is_pricing_visible BOOLEAN NOT NULL DEFAULT TRUE,
    is_wp_org_compliant BOOLEAN NOT NULL DEFAULT FALSE,
    installs_count INTEGER NOT NULL DEFAULT 0,
    active_installs_count INTEGER NOT NULL DEFAULT 0,
    free_releases_count INTEGER NOT NULL DEFAULT 0,
    premium_releases_count INTEGER NOT NULL DEFAULT 0,
    total_purchases INTEGER NOT NULL DEFAULT 0,
    total_subscriptions INTEGER NOT NULL DEFAULT 0,
    total_renewals INTEGER NOT NULL DEFAULT 0,
    total_failed_purchases INTEGER NOT NULL DEFAULT 0,
    earnings DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    type product_type NOT NULL DEFAULT 'plugin',
    is_static BOOLEAN NOT NULL DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Installations table (Freemius installations)
CREATE TABLE IF NOT EXISTS installations (
    id BIGSERIAL PRIMARY KEY,
    freemius_id BIGINT UNIQUE NOT NULL,
    secret_key VARCHAR(255) NOT NULL,
    public_key VARCHAR(255) NOT NULL,
    site_id BIGINT NOT NULL,
    plugin_id BIGINT NOT NULL,
    user_id BIGINT NOT NULL,
    url VARCHAR(500) NULL,
    title VARCHAR(255) NULL,
    version VARCHAR(50) NOT NULL,
    plan_id BIGINT NULL,
    license_id BIGINT NULL,
    trial_plan_id BIGINT NULL,
    trial_ends TIMESTAMP WITH TIME ZONE NULL,
    subscription_id BIGINT NULL,
    gross DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    country_code VARCHAR(2) NULL,
    language VARCHAR(10) NULL,
    platform_version VARCHAR(50) NULL,
    sdk_version VARCHAR(50) NULL,
    programming_language_version VARCHAR(50) NULL,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    is_disconnected BOOLEAN NOT NULL DEFAULT FALSE,
    is_premium BOOLEAN NOT NULL DEFAULT FALSE,
    is_uninstalled BOOLEAN NOT NULL DEFAULT FALSE,
    is_locked BOOLEAN NOT NULL DEFAULT FALSE,
    source INTEGER NOT NULL DEFAULT 0, -- migration source
    upgraded TIMESTAMP WITH TIME ZONE NULL,
    last_seen_at TIMESTAMP WITH TIME ZONE NULL,
    last_served_update_version VARCHAR(50) NULL,
    is_beta BOOLEAN NOT NULL DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- IP data table (ipRegistry data storage)
CREATE TABLE IF NOT EXISTS ip_data (
    id BIGSERIAL PRIMARY KEY,
    ip_address INET UNIQUE NOT NULL,
    raw_data JSONB NOT NULL, -- Complete ipRegistry response stored as-is
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- API logs table (request/response logging)
CREATE TABLE IF NOT EXISTS api_logs (
    id BIGSERIAL PRIMARY KEY,
    ip_address INET NOT NULL,
    method VARCHAR(10) NOT NULL,
    endpoint VARCHAR(255) NOT NULL,
    request_data JSONB NULL,
    response_status INTEGER NOT NULL,
    response_data JSONB NULL,
    processing_time INTEGER NOT NULL DEFAULT 0, -- milliseconds
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Admin users table (system administrators)
CREATE TABLE IF NOT EXISTS admin_users (
    id BIGSERIAL PRIMARY KEY,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    role user_role NOT NULL DEFAULT 'dev',
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    last_login TIMESTAMP WITH TIME ZONE NULL,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Job queue table for background job processing (fallback when Redis is not available)
CREATE TABLE IF NOT EXISTS job_queue (
    id VARCHAR(255) PRIMARY KEY,
    class VARCHAR(255) NOT NULL,
    data JSONB NOT NULL,
    queue VARCHAR(100) NOT NULL DEFAULT 'default',
    attempts INTEGER NOT NULL DEFAULT 0,
    max_attempts INTEGER NOT NULL DEFAULT 3,
    timeout INTEGER NOT NULL DEFAULT 300,
    created_at BIGINT NOT NULL,
    available_at BIGINT NOT NULL,
    reserved_at BIGINT NULL,
    failed_at BIGINT NULL,
    error TEXT NULL
);

-- Add foreign key constraints with conflict protection
DO $$ BEGIN
    ALTER TABLE installations 
    ADD CONSTRAINT IF NOT EXISTS fk_installations_plugin_id 
        FOREIGN KEY (plugin_id) REFERENCES products(freemius_id) 
        ON DELETE CASCADE ON UPDATE CASCADE;
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Create indexes for performance optimization with conflict protection

-- Products table indexes
CREATE INDEX IF NOT EXISTS idx_products_freemius_id ON products(freemius_id);
CREATE INDEX IF NOT EXISTS idx_products_slug ON products(slug);
CREATE INDEX IF NOT EXISTS idx_products_is_released ON products(is_released);
CREATE INDEX IF NOT EXISTS idx_products_environment ON products(environment);
CREATE INDEX IF NOT EXISTS idx_products_created_at ON products(created_at);

-- Installations table indexes
CREATE INDEX IF NOT EXISTS idx_installations_freemius_id ON installations(freemius_id);
CREATE INDEX IF NOT EXISTS idx_installations_plugin_id ON installations(plugin_id);
CREATE INDEX IF NOT EXISTS idx_installations_site_id ON installations(site_id);
CREATE INDEX IF NOT EXISTS idx_installations_user_id ON installations(user_id);
CREATE INDEX IF NOT EXISTS idx_installations_is_active ON installations(is_active);
CREATE INDEX IF NOT EXISTS idx_installations_is_uninstalled ON installations(is_uninstalled);
CREATE INDEX IF NOT EXISTS idx_installations_is_premium ON installations(is_premium);
CREATE INDEX IF NOT EXISTS idx_installations_license_id ON installations(license_id);
CREATE INDEX IF NOT EXISTS idx_installations_last_seen_at ON installations(last_seen_at);
CREATE INDEX IF NOT EXISTS idx_installations_created_at ON installations(created_at);

-- IP data table indexes
CREATE INDEX IF NOT EXISTS idx_ip_data_ip_address ON ip_data(ip_address);
CREATE INDEX IF NOT EXISTS idx_ip_data_created_at ON ip_data(created_at);
CREATE INDEX IF NOT EXISTS idx_ip_data_updated_at ON ip_data(updated_at);

-- API logs table indexes
CREATE INDEX IF NOT EXISTS idx_api_logs_ip_address ON api_logs(ip_address);
CREATE INDEX IF NOT EXISTS idx_api_logs_endpoint ON api_logs(endpoint);
CREATE INDEX IF NOT EXISTS idx_api_logs_method ON api_logs(method);
CREATE INDEX IF NOT EXISTS idx_api_logs_response_status ON api_logs(response_status);
CREATE INDEX IF NOT EXISTS idx_api_logs_created_at ON api_logs(created_at);

-- Admin users table indexes
CREATE INDEX IF NOT EXISTS idx_admin_users_email ON admin_users(email);
CREATE INDEX IF NOT EXISTS idx_admin_users_role ON admin_users(role);
CREATE INDEX IF NOT EXISTS idx_admin_users_is_active ON admin_users(is_active);

-- Job queue indexes
CREATE INDEX IF NOT EXISTS idx_job_queue_queue_available ON job_queue(queue, available_at) WHERE reserved_at IS NULL AND failed_at IS NULL;
CREATE INDEX IF NOT EXISTS idx_job_queue_reserved ON job_queue(reserved_at) WHERE reserved_at IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_job_queue_failed ON job_queue(failed_at) WHERE failed_at IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_job_queue_created ON job_queue(created_at);

-- Create functions for automatic timestamp updates with conflict protection
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for automatic timestamp updates with conflict protection
DO $$ BEGIN
    CREATE TRIGGER update_products_updated_at 
        BEFORE UPDATE ON products 
        FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TRIGGER update_installations_updated_at 
        BEFORE UPDATE ON installations 
        FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TRIGGER update_ip_data_updated_at 
        BEFORE UPDATE ON ip_data 
        FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TRIGGER update_admin_users_updated_at 
        BEFORE UPDATE ON admin_users 
        FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Insert initial super admin user with conflict protection
-- Password hash for 'admin123456789' using PHP password_hash()
INSERT INTO admin_users (email, password_hash, role)
VALUES ('<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'super_admin')
ON CONFLICT (email) DO NOTHING;

-- Insert test admin users for different roles (password: 'testpassword123')
INSERT INTO admin_users (email, password_hash, role, is_active) VALUES
('<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'dev', true),
('<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'marketing', true),
('<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'sales', true)
ON CONFLICT (email) DO NOTHING;

-- Create views for common queries with conflict protection

-- Active installations view
CREATE OR REPLACE VIEW active_installations AS
SELECT 
    i.*,
    p.title as product_title,
    p.slug as product_slug
FROM installations i
JOIN products p ON i.plugin_id = p.freemius_id
WHERE i.is_active = TRUE 
  AND i.is_uninstalled = FALSE;

-- Fresh IP data view (data less than 3 days old)
CREATE OR REPLACE VIEW fresh_ip_data AS
SELECT *
FROM ip_data
WHERE updated_at > (CURRENT_TIMESTAMP - INTERVAL '3 days');

-- API statistics view
CREATE OR REPLACE VIEW api_statistics AS
SELECT 
    DATE(created_at) as date,
    COUNT(*) as total_requests,
    COUNT(CASE WHEN response_status < 400 THEN 1 END) as successful_requests,
    COUNT(CASE WHEN response_status >= 400 THEN 1 END) as failed_requests,
    AVG(processing_time) as avg_processing_time
FROM api_logs
GROUP BY DATE(created_at)
ORDER BY date DESC;

-- Grant permissions (uncomment and adjust as needed for your environment)
-- GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO guardgeo_user;
-- GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO guardgeo_user;
-- GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA public TO guardgeo_user;
